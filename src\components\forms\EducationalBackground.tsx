import { <PERSON><PERSON> } from "@/components/ui/button";
import { useFormContext } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { EducationalBackgroundValidation } from "@/features/auth/schema";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronDown, ChevronLeft, X } from "react-feather";
import {
	subjectGroups,
	targetEntryTests,
} from "@/lib/constants/onboardingvalues";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";

const EducationalBackground = ({
	goBack,
	loading = false,
}: {
	loading: boolean;
	goBack: () => void;
}) => {
	const [selectedSubjectGroup, setSelectedSubjectGroup] = useState(
		() => new Set<string>()
	);
	const [selectedTests, setSelectedTests] = useState(() => new Set<string>());
	const [subjectGroupOpen, setSubjectGroupOpen] = useState(false);
	const [testsOpen, setTestsOpen] = useState(false);
	// const selectedTestsArray = Array.from(selectedTests);

	const form = useFormContext<EducationalBackgroundValidation>();

	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<ChevronLeft
				onClick={goBack}
				className="self-start hover:cursor-pointer"
			/>
			<p className="text-gray-600 text-center max-w-[400px]">
				Great! We just have a few more questions.
			</p>
			<h1>Educational Background</h1>
			<div className="w-full flex flex-col gap-y-4">
				<FormField
					control={form.control}
					name="subjectGroup"
					render={() => (
						<FormItem className="flex flex-col">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								Education Stream
								<div className="text-gray-400 text-xs font-normal mt-1">
									O/A Level students may select based on their chosen subjects
								</div>
							</FormLabel>
							<Popover
								open={subjectGroupOpen}
								onOpenChange={setSubjectGroupOpen}
							>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="relative flex min-h-[36px] items-start justify-between rounded-md border data-[state=open]:border-ring w-full">
											<div className="flex items-center px-3 py-1 gap-1 flex-1 min-w-0 overflow-x-auto scrollbar-hide">
												{selectedSubjectGroup?.size > 0 ? (
													subjectGroups &&
													subjectGroups
														.filter((subjectGroup) =>
															selectedSubjectGroup.has(subjectGroup.value)
														)
														.map((subjectGroup) => (
															<Badge
																key={subjectGroup.value}
																variant="outline"
																className="gap-1 pr-0.5 flex-shrink-0"
															>
																<span className="">{subjectGroup.label}</span>
																<span
																	onClick={(e) => {
																		e.preventDefault();
																		setSelectedSubjectGroup((prev) => {
																			const next = new Set(prev);
																			next.delete(subjectGroup.value);
																			return next;
																		});
																	}}
																	className="flex items-center rounded-sm px-[1px] hover:bg-accent hover:text-red-500"
																>
																	<X size={14} />
																</span>
															</Badge>
														))
												) : (
													<span className="mr-auto text-sm">Select...</span>
												)}
											</div>
											<div className="flex flex-shrink-0 items-center self-stretch px-1 text-muted-foreground/60">
												{selectedSubjectGroup?.size > 0 && (
													<div
														onClick={(e) => {
															e.preventDefault();
															setSelectedSubjectGroup(new Set());
														}}
														className="flex items-center self-stretch p-2 hover:text-red-500"
													>
														<X size={16} />
													</div>
												)}
												<span className="mx-0.5 my-2 w-[1px] self-stretch bg-border" />
												<div className="flex items-center self-stretch p-2 hover:text-muted-foreground">
													<ChevronDown size={16} />
												</div>
											</div>
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-[var(--radix-popover-trigger-width)] p-0"
									align="start"
								>
									<Command>
										<CommandInput placeholder="Search" className="h-9" />
										<CommandEmpty>No result found.</CommandEmpty>
										<CommandGroup>
											{subjectGroups.map((subjectGroup, index) => {
												const isSelected = selectedSubjectGroup.has(
													subjectGroup.value
												);
												return (
													<CommandItem
														key={index}
														onSelect={() => {
															if (isSelected) {
																selectedSubjectGroup.delete(subjectGroup.value);
															} else {
																selectedSubjectGroup.add(subjectGroup.value);
															}
															const filterValues =
																Array.from(selectedSubjectGroup);
															form.setValue("subjectGroup", filterValues);
														}}
													>
														<div
															className={cn(
																"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
																isSelected
																	? "bg-primary text-primary-foreground"
																	: "opacity-50 [&_svg]:invisible"
															)}
														>
															<Check className={cn("h-4 w-4")} />
														</div>
														<span>{subjectGroup.label}</span>
													</CommandItem>
												);
											})}
										</CommandGroup>
										<div className="p-2 border-t">
											<Button
												onClick={() => setSubjectGroupOpen(false)}
												className="w-full"
												size="sm"
											>
												Done
											</Button>
										</div>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="targetEntryTests"
					render={() => (
						<FormItem className="flex flex-col">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								Tests
							</FormLabel>
							<Popover open={testsOpen} onOpenChange={setTestsOpen}>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="relative flex min-h-[36px] items-start justify-between rounded-md border data-[state=open]:border-ring w-full">
											<div className="flex items-center px-3 py-1 gap-1 flex-1 min-w-0 overflow-x-auto scrollbar-hide">
												{selectedTests?.size > 0 ? (
													targetEntryTests &&
													targetEntryTests
														.filter((targetEntryTest) =>
															selectedTests.has(targetEntryTest.value)
														)
														.map((targetEntryTest) => (
															<Badge
																key={targetEntryTest.value}
																variant="outline"
																className="gap-1 pr-0.5 flex-shrink-0"
															>
																<span className="">
																	{targetEntryTest.label}
																</span>
																<span
																	onClick={(e) => {
																		e.preventDefault();
																		setSelectedTests((prev) => {
																			const next = new Set(prev);
																			next.delete(targetEntryTest.value);
																			return next;
																		});
																	}}
																	className="flex items-center rounded-sm px-[1px] hover:bg-accent hover:text-red-500"
																>
																	<X size={14} />
																</span>
															</Badge>
														))
												) : (
													<span className="mr-auto text-sm">Select...</span>
												)}
											</div>
											<div className="flex flex-shrink-0 items-center self-stretch px-1 text-muted-foreground/60">
												{selectedTests?.size > 0 && (
													<div
														onClick={(e) => {
															e.preventDefault();
															setSelectedTests(new Set());
														}}
														className="flex items-center self-stretch p-2 hover:text-red-500"
													>
														<X size={16} />
													</div>
												)}
												<span className="mx-0.5 my-2 w-[1px] self-stretch bg-border" />
												<div className="flex items-center self-stretch p-2 hover:text-muted-foreground">
													<ChevronDown size={16} />
												</div>
											</div>
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-[var(--radix-popover-trigger-width)] p-0"
									align="start"
								>
									<Command>
										<CommandInput placeholder="Search" className="h-9" />
										<CommandEmpty>No result found.</CommandEmpty>
										<CommandGroup>
											{targetEntryTests.map((targetEntryTest, index) => {
												const isSelected = selectedTests.has(
													targetEntryTest.value
												);
												return (
													<CommandItem
														key={index}
														onSelect={() => {
															if (isSelected) {
																selectedTests.delete(targetEntryTest.value);
															} else {
																selectedTests.add(targetEntryTest.value);
															}
															const filterValues = Array.from(selectedTests);
															form.setValue("targetEntryTests", filterValues);
														}}
													>
														<div
															className={cn(
																"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
																isSelected
																	? "bg-primary text-primary-foreground"
																	: "opacity-50 [&_svg]:invisible"
															)}
														>
															<Check className={cn("h-4 w-4")} />
														</div>
														<span>{targetEntryTest.label}</span>
													</CommandItem>
												);
											})}
										</CommandGroup>
										<div className="p-2 border-t">
											<Button
												onClick={() => setTestsOpen(false)}
												className="w-full"
												size="sm"
											>
												Done
											</Button>
										</div>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* <div className="flex gap-x-4  overflow-x-auto h-8 whitespace-nowrap no-scrollbar pr-4">
					{selectedTestsArray.map((targetEntryTest) => (
						<Badge
							variant="outline"
							className="py-1.5  px-3 text-sm shadcn bg-gray-50 border-gray-300 font-medium whitespace-nowrap"
						>
							{targetEntryTest}
							<X
								className="size-4 ml-2 hover:cursor-pointer"
								onClick={(e) => {
									e.preventDefault();
									setSelectedTests((prev) => {
										const next = new Set(prev);
										next.delete(targetEntryTest);
										return next;
									});
								}}
							/>
						</Badge>
					))}
				</div> */}

				<Button loading={loading} type="submit" className="w-full ">
					Next
				</Button>
			</div>
		</div>
	);
};

export default EducationalBackground;
